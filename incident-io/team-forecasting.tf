data "incident_user" "sean" {
  email = "<EMAIL>"
}

data "incident_user" "alex" {
  email = "<EMAIL>"
}

module "forecasting_team" {
  source = "./modules/incident-team"

  team_name = "Forecasting"
  schedule_versions = [
    {
      effective_from    = "2025-04-21T00:00:00Z"
      handover_start_at = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.sean.id,
        data.incident_user.alex.id
      ]
    }
  ]
  escalation_user_id = data.incident_user.michael.id
  slack_channel_id   = "C0882PR2XB7" # bot-ops-forecasting-prod
}
