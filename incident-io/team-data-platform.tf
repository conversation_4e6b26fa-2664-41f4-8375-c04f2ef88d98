data "incident_user" "kamilla" {
  email = "<EMAIL>"
}

data "incident_user" "rob" {
  email = "<EMAIL>"
}


module "data_platform_ops_team" {
  source = "./modules/incident-team"

  team_name = "Data Platform"
  versions = [
    {
      effective_from    = "2025-04-21T00:00:00Z"
      handover_start_at = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.rob.id,
        data.incident_user.kamilla.id,
      ]
    }
  ]
  escalation_user_id = data.incident_user.michael.id
  slack_channel_id   = "C088851CU8H" # bot-ops-data-platform-prod
}
