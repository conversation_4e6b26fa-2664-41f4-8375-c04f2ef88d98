data "incident_user" "chase" {
  email = "<EMAIL>"
}

data "incident_user" "luke" {
  email = "<EMAIL>"
}

data "incident_user" "ami" {
  email = "<EMAIL>"
}

data "incident_user" "andrea" {
  email = "<EMAIL>"
}

data "incident_user" "anna" {
  email = "<EMAIL>"
}

data "incident_user" "darren" {
  email = "<EMAIL>"
}

module "orca_orion_team" {
  source = "./modules/incident-team"

  team_name = "Orca Orion"
  schedule_versions = [
    {
      effective_from    = "2025-04-21T00:00:00Z"
      handover_start_at = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.darren.id,
        data.incident_user.chase.id,
        data.incident_user.ami.id,
        data.incident_user.luke.id,
        data.incident_user.andrea.id,
        data.incident_user.anna.id,
      ]
    }
  ]
  escalation_user_id = data.incident_user.juandiego.id
  slack_channel_id   = "C03SK2WLHF1" # team-observations-alert-prod
}
