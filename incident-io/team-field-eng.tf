data "incident_user" "nathaniel" {
  email = "<EMAIL>"
}

data "incident_user" "noah" {
  email = "<EMAIL>"
}

data "incident_user" "leo" {
  email = "<EMAIL>"
}

module "field_eng_team" {
  source = "./modules/incident-team"

  team_name = "Field Eng"
  versions = [
    {
      effective_from    = "2025-04-21T00:00:00Z"
      handover_start_at = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.noah.id,
        data.incident_user.nathaniel.id,
      ]
    },
    {
      effective_from    = "2025-04-21T00:00:00Z"
      handover_start_at = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.noah.id,
        data.incident_user.nathaniel.id,
      ]
    }
  ]
  escalation_user_id = data.incident_user.dorian.id
  slack_channel_id   = "C08HX98JFCN" # team-field-eng-alert-prod
}
