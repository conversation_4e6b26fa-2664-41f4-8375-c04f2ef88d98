locals {
  autoedge_roles = [
    "roles/secretmanager.secretAccessor",
    "roles/iam.serviceAccountTokenCreator"
  ]
}

resource "google_service_account" "autoedge_sa" {
  account_id   = "autoedge"
  project      = var.project_id
  display_name = "Edge Management Service Account"
}

resource "google_project_iam_member" "autoedge_roles" {
  for_each = toset(local.autoedge_roles)
  project  = var.project_id
  role     = each.key
  member   = "serviceAccount:${google_service_account.autoedge_sa.email}"
}


resource "google_iam_workload_identity_pool" "autoedge" {
  workload_identity_pool_id = "autoedge-github"
  display_name              = "Autoedge Github pool"
  description               = "Identity pool for access to Apella Cloud API from k8s-edge repo"
}


resource "google_iam_workload_identity_pool_provider" "autoedge" {
  workload_identity_pool_id          = google_iam_workload_identity_pool.autoedge.workload_identity_pool_id
  workload_identity_pool_provider_id = "autoedge-prvdr"
  display_name                       = "Autoedge Identity provider"
  attribute_mapping = {
    "google.subject"             = "assertion.sub"
    "attribute.actor"            = "assertion.actor"
    "attribute.repository"       = "assertion.repository"
    "attribute.repository_owner" = "assertion.repository_owner"
  }
  attribute_condition = "assertion.repository_owner==\"Apella-Technology\""

  oidc {
    issuer_uri = "https://token.actions.githubusercontent.com"
  }
}


resource "google_service_account_iam_binding" "autoedge-account" {
  service_account_id = google_service_account.autoedge_sa.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "principalSet://iam.googleapis.com/${google_iam_workload_identity_pool.autoedge.name}/attribute.repository/Apella-Technology/k8s-edge"
  ]
}
